use proc_macro::TokenStream;
use quote::quote;
use syn::{DeriveInput, parse_macro_input};

#[proc_macro_derive(GetFields)]
pub fn get_fields_derive(input: TokenStream) -> TokenStream {
    let input = parse_macro_input!(input as DeriveInput);
    let name = &input.ident;

    let fields = if let syn::Data::Struct(s) = input.data {
        s.fields.into_iter().map(|f| {
            let ident = f.ident.unwrap();
            ident.to_string()
        })
    } else {
        panic!("GetFields can only be used on structs");
    };

    let expanded = quote! {
        impl #name {
            pub fn get_field_names() -> &'static [&'static str] {
                &[#(#fields),*]
            }
        }
    };

    TokenStream::from(expanded)
}
